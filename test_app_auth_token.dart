import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Test script to check if the app has a valid auth token and test promo code verification
class AppAuthTokenTester {
  static const String baseUrl = 'https://api2.eeil.online/api/v1';
  static const String verifyEndpoint = '$baseUrl/user/promocodes/verify';

  /// Main test function
  static Future<void> testAppAuthToken() async {
    print('🔔 ===== APP AUTH TOKEN TESTING =====');
    print('🔔 Checking for stored authentication token...\n');

    try {
      // Initialize SharedPreferences
      final prefs = await SharedPreferences.getInstance();

      // Check for auth token in different possible keys
      final tokenKeys = [
        'auth_token',
        'token',
        'user_token',
        'access_token',
        'bearer_token',
      ];

      String? foundToken;
      String? foundKey;

      for (final key in tokenKeys) {
        final token = prefs.getString(key);
        if (token != null && token.isNotEmpty) {
          foundToken = token;
          foundKey = key;
          break;
        }
      }

      if (foundToken != null) {
        print('✅ Auth token found!');
        print('🔑 Token key: $foundKey');
        print(
            '🔑 Token preview: ${foundToken.substring(0, foundToken.length > 20 ? 20 : foundToken.length)}...');
        print('🔑 Token length: ${foundToken.length} characters\n');

        // Test promo code verification with the real token
        await testPromoCodeWithRealToken(foundToken);

        // Check user data
        await checkUserData(prefs);
      } else {
        print('❌ No auth token found in SharedPreferences');
        print('📝 Available keys in SharedPreferences:');

        final allKeys = prefs.getKeys();
        for (final key in allKeys) {
          final value = prefs.get(key);
          print(
              '   - $key: ${value.runtimeType} (${value.toString().length} chars)');
        }

        print(
            '\n📝 This means the user is not logged in or the token is stored elsewhere.');
        print(
            '📝 Please log in through the app first, then run this test again.');
      }
    } catch (e) {
      print('❌ Error accessing SharedPreferences: $e');
    }
  }

  /// Test promo code verification with real auth token
  static Future<void> testPromoCodeWithRealToken(String authToken) async {
    print('🧪 TESTING PROMO CODE VERIFICATION WITH REAL TOKEN');
    print('=' * 60);

    final testCodes = ['SAVE15', 'WELCOME10', 'TEST123'];

    for (final code in testCodes) {
      print('🎫 Testing promo code: $code');

      try {
        final payload = {'code': code};

        final response = await http
            .post(
              Uri.parse(verifyEndpoint),
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': 'Bearer $authToken',
              },
              body: jsonEncode(payload),
            )
            .timeout(Duration(seconds: 15));

        print('📊 Status: ${response.statusCode}');
        print('📥 Response: ${response.body}');

        if (response.statusCode == 200) {
          try {
            final responseData = jsonDecode(response.body);
            if (responseData['success'] == true) {
              print('✅ SUCCESS: $code is valid!');
              if (responseData['data'] != null) {
                final data = responseData['data'];
                print('💰 Credits: ${data['credits'] ?? 'N/A'}');
                print(
                    '💵 Min Amount: ${data['minimum_amount_applicable'] ?? 'N/A'}');
                print('📝 Description: ${data['description'] ?? 'N/A'}');
              }
            } else {
              print(
                  '❌ INVALID: ${responseData['message'] ?? 'Promo code not valid'}');
            }
          } catch (e) {
            print('❌ JSON Parse Error: $e');
          }
        } else if (response.statusCode == 401) {
          print('❌ AUTHENTICATION FAILED: Token is invalid or expired');
          print('📝 Please log in again through the app');
        } else if (response.statusCode == 404) {
          print('❌ ENDPOINT NOT FOUND: API endpoint may have changed');
        } else {
          print('❌ UNEXPECTED STATUS: ${response.statusCode}');
        }
      } catch (e) {
        print('❌ Request error for $code: $e');
      }
      print('');
    }
  }

  /// Check user data in SharedPreferences
  static Future<void> checkUserData(SharedPreferences prefs) async {
    print('🧪 CHECKING USER DATA');
    print('=' * 30);

    final userDataKeys = [
      'user_data',
      'userData',
      'user',
      'current_user',
      'logged_in_user',
    ];

    for (final key in userDataKeys) {
      final userData = prefs.getString(key);
      if (userData != null && userData.isNotEmpty) {
        print('✅ User data found in key: $key');
        try {
          final parsedData = jsonDecode(userData);
          print('👤 User ID: ${parsedData['id'] ?? 'N/A'}');
          print('👤 Name: ${parsedData['name'] ?? 'N/A'}');
          print('👤 Email: ${parsedData['email'] ?? 'N/A'}');
          print(
              '👤 Phone: ${parsedData['mobile_number'] ?? parsedData['phone'] ?? 'N/A'}');
        } catch (e) {
          print('❌ Error parsing user data: $e');
          print(
              '📄 Raw data: ${userData.substring(0, userData.length > 100 ? 100 : userData.length)}...');
        }
        break;
      }
    }
    print('');
  }

  /// Check login status
  static Future<void> checkLoginStatus(SharedPreferences prefs) async {
    print('🧪 CHECKING LOGIN STATUS');
    print('=' * 30);

    final loginKeys = [
      'is_logged_in',
      'isLoggedIn',
      'logged_in',
      'user_logged_in',
    ];

    for (final key in loginKeys) {
      final isLoggedIn = prefs.getBool(key);
      if (isLoggedIn != null) {
        print('✅ Login status found in key: $key');
        print('🔐 Is logged in: $isLoggedIn');
        break;
      }
    }
    print('');
  }
}

/// Main function
void main() async {
  await AppAuthTokenTester.testAppAuthToken();
}
